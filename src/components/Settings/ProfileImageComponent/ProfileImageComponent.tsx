import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import ImagePicker, { ImageOrVideo } from 'react-native-image-crop-picker';
import { BigCameraIcon } from '~assets/icons';
import handleGoToSettingsImage from '~Utils/Settings';
import storage from '@react-native-firebase/storage';
import auth from '@react-native-firebase/auth';
import { useTheme } from '~contexts/ThemeContext';
import CircularImage from '~components/CircularImage';
import { spacing, typography } from '~constants/design';

interface IProps {
  isFromOnboarding?: boolean;
  errorText?: any;
  value?: string;
  onChange: (value: string) => void;
  name?: string;
  email?: string;
}

const ProfileImageComponent = ({ isFromOnboarding, errorText, value, onChange, name, email }: IProps) => {
  const { colors } = useTheme();

  const saveImageToDB = async (image: ImageOrVideo) => {
    try {
      if (!isFromOnboarding) {
        await storage().ref('users/' + auth().currentUser!.uid + '/profile.png').putFile(image.path);
        await storage()
          .ref('users/' + auth().currentUser!.uid + '/profile.png')
          .getDownloadURL()
          .then(async url => {
            onChange(url);
          });
        return;
      }
      onChange(image.path);
    } catch (error) {
      console.log(error);
    }
  };

  const selectImageFromGallery = async () => {
    try {
      const image = await ImagePicker.openPicker({
        width: 600,
        height: 600,
        cropping: true,
      });

      await saveImageToDB(image);
    } catch (error: any) {
      if (error.message && error.message !== 'User cancelled image selection') {
        handleGoToSettingsImage();
      }
    }
  };

  const modernStyles = StyleSheet.create({
    container: {
      alignItems: 'center',
      paddingVertical: spacing.lg,
    },
    imageContainer: {
      position: 'relative',
      marginBottom: spacing.md,
    },
    cameraButton: {
      position: 'absolute',
      bottom: 0,
      right: 0,
      backgroundColor: colors.primary,
      borderRadius: 20,
      width: 30,
      height: 30,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 3,
      borderColor: colors.cardBackground,
      elevation: 3,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
    },
    nameText: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.bold,
      color: colors.textPrimary,
      textAlign: 'center',
      marginBottom: spacing.xs,
    },
    emailText: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.normal,
      color: colors.textSecondary,
      textAlign: 'center',
    },
    errorText: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.normal,
      color: colors.error,
      textAlign: 'center',
      marginTop: spacing.sm,
    },
  });

  return (
    <View style={modernStyles.container}>
      <View style={modernStyles.imageContainer}>
        <CircularImage
          source={
            value
              ? {
                uri: value,
                priority: 'high',
                cache: 'immutable',
              }
              : require('~assets/images/eventPhotoPlaceholder.png')
          }
          size={150}
          showShadow={true}
        />

        <TouchableOpacity onPress={selectImageFromGallery} style={modernStyles.cameraButton}>
          <BigCameraIcon />
        </TouchableOpacity>
      </View>

      {!isFromOnboarding && (
        <>
          <Text style={modernStyles.nameText}>{name}</Text>
          <Text style={modernStyles.emailText}>{email}</Text>
        </>
      )}

      {errorText && <Text style={modernStyles.errorText}>{errorText}</Text>}
    </View>
  );
};

export default ProfileImageComponent;
