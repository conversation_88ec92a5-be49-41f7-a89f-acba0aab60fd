import auth from '@react-native-firebase/auth';
import storage from '@react-native-firebase/storage';
import { RouteProp, useIsFocused, useNavigation, useRoute } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, Platform, Text, TextInput, View, TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { organizeByCategory } from '~Utils/filterSubcategories';
import { logScreenView } from '~Utils/firebaseAnalytics';
import ModernButton from '~components/ModernButton';
import { GoBackHeader } from '~components/GoBackHeader';
import ProgressDots from '~components/ProgressDots';
import SubcategoriesLists from '~components/SubcategoriesLists';
import { SCREENS } from '~constants';
import { useGetCategories } from '~hooks/subcategories/useGetCategories';
import { useGetSubcategories } from '~hooks/subcategories/useGetSubcategories';
import { useCreateUser } from '~hooks/user/useCreateUser';
import { useUpdateUser } from '~hooks/user/useUpdateUser';
import { useUpdateUserGroups } from '~hooks/user/useUpdateUserGroups';
import { useUpdateUserSubcategories } from '~hooks/user/useUpdateUserSubcategories';
import { useOnboardingStore } from '~providers/onboarding/zustand';
import { useUserStore } from '~providers/userStore/zustand';
import { SubCategoryType } from '~types/categories';
import { NavigationProps, RootStackParamsList } from '~types/navigation/navigation.type';
import { useTheme } from '~contexts/ThemeContext';

const CategoryScrollView = () => {
  const { colors } = useTheme();
  const { params } = useRoute<RouteProp<RootStackParamsList, SCREENS.ONBOARDING_SUBCATEGORIES>>();
  const { t } = useTranslation();
  const { bottom } = useSafeAreaInsets();
  const { mutateAsync } = useCreateUser();
  const {
    personalOnboarding,
    categories: selectedCategories,
    subcategories: selectedSubCategories,
  } = useOnboardingStore();
  const { data } = useGetSubcategories();
  const { data: d } = useGetCategories();
  const { user } = useUserStore();
  const { mutateAsync: updateUserSubcategories } = useUpdateUserSubcategories();
  const { mutateAsync: updateUserGroups } = useUpdateUserGroups();
  const { navigate } = useNavigation<NavigationProps>();
  const isBusiness = params?.isBusiness;

  const [categories, setCategories] = useState<Record<string, SubCategoryType[]> | null>(null);
  const [searchValue, setSearchValue] = useState('');
  const [searchQuery, setSearchQuery] = useState(''); // BUG-035 FIX: Separate search input from actual search query

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Categories', 'CategoryScrollView');
  }, []);

  useEffect(() => {
    if (d && data && !searchQuery) {
      setCategories(organizeByCategory(selectedCategories, d, data));
    }
  }, [d, data, searchQuery]);

  const submit = async () => {
    setIsLoading(true);

    if (isBusiness) {
      navigate(SCREENS.ONBOARDING_GROUP, { isBusiness: true });
    }

    try {
      if (user) {
        setIsLoading(true);
        await updateUserGroups({ ids: personalOnboarding.groups });
        await updateUserSubcategories({ ids: selectedSubCategories });
        navigate(SCREENS.EDIT_PREFERANCE, { setting: false });
        return;
      }

      if (personalOnboarding.photo[0] != 'h') {
        await storage()
          .ref('users/' + auth().currentUser!.uid + '/profile.png')
          .putFile(personalOnboarding.photo);
      }

      const urlPhoto =
        personalOnboarding.photo[0] == 'h'
          ? auth().currentUser?.photoURL
          : await storage()
            .ref('users/' + auth().currentUser!.uid + '/profile.png')
            .getDownloadURL();

      await mutateAsync({
        ...personalOnboarding,
        email: auth().currentUser!.email!,
        photo: urlPhoto,
        uid: auth().currentUser!.uid,
        is_registration_finished: false,
        subcategories: selectedSubCategories,
        coords_real: null,
      });
      navigate(SCREENS.EDIT_PREFERANCE, { setting: false });
    } catch (E) {
      setIsLoading(false);
      console.log(E);
    }
  };

  useEffect(() => {
    handleSearchCategory();
  }, [searchQuery]);

  const handleSearchCategory = () => {
    if (searchQuery?.length && data && d) {
      const inputData = data?.filter(
        (item: any) => item?.subcategory_name?.toLowerCase().indexOf(searchQuery.trim().toLowerCase()) !== -1,
      );
      // BUG-031 FIX: Include selected categories in search results
      setCategories(organizeByCategory(selectedCategories, d, inputData));
    } else if (data && d) {
      setCategories(organizeByCategory(selectedCategories, d, data));
    }
  };

  // BUG-035 FIX: Add search button functionality
  const handleSearch = () => {
    setSearchQuery(searchValue);
  };

  const onSubmitPress = () => {
    if (selectedSubCategories.length > 0) {
      submit();
    } else {
      Alert.alert('You can modify your selections later in settings', '', [{ text: 'OK', onPress: () => submit() }]);
    }
  };

  const isButtonDisabled = selectedSubCategories.length == 0;

  return (
    <>
      <GoBackHeader />
      <View style={{ flex: 1, paddingTop: 50, backgroundColor: colors.white, marginTop: 20 }}>
        <View style={{ width: '100%', alignItems: 'flex-start', paddingHorizontal: 16 }}>
          <Text
            style={{
              textAlign: 'left',
              fontSize: 24,
              color: colors.textPrimary,
              fontWeight: '700',
              marginTop: 12,
              marginBottom: 16,
            }}>
            {t('signin.tell_us_what_you_like')}
          </Text>
        </View>

        <View
          style={{
            paddingHorizontal: 16,
            marginBottom: 16,
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <TextInput
            placeholder="Search by name"
            value={searchValue}
            onChangeText={setSearchValue}
            onSubmitEditing={handleSearch} // BUG-035 FIX: Allow search on enter
            returnKeyType="search"
            style={{
              flex: 1,
              height: 40,
              borderWidth: 1,
              borderColor: colors.textSecondary,
              borderRadius: 5,
              paddingHorizontal: 10,
              marginRight: 8,
            }}
          />
          <TouchableOpacity
            onPress={handleSearch}
            style={{
              height: 40,
              paddingHorizontal: 16,
              backgroundColor: colors.primary,
              borderRadius: 5,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Text style={{ color: colors.white, fontWeight: '600' }}>Search</Text>
          </TouchableOpacity>
        </View>

        <View style={{ flex: 1, paddingBottom: 120 }}>
          <SubcategoriesLists searchValue={searchValue} categoriesList={d} filteredSubcategoriesData={categories} />
        </View>

        <View
          style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            backgroundColor: colors.white,
            paddingHorizontal: 16,
            paddingBottom: Platform.OS === 'android' ? 20 : bottom,
            paddingTop: 16,
            borderTopWidth: 1,
            borderTopColor: colors.separatorLine,
          }}>
          <ModernButton
            title={isButtonDisabled ? t('signin.i_will_do_later') : t('generic.submit')}
            loading={isLoading}
            onPress={() => onSubmitPress()}
            variant="primary"
            size="lg"
            fullWidth
            style={{ marginBottom: 24 }}
          />
          <ProgressDots dotsNumber={isBusiness ? 2 : 6} selectedDotNumber={isBusiness ? 2 : 3} />
        </View>
      </View>
    </>
  );
};

export default CategoryScrollView;
