import { Navigation<PERSON>ontainer, NavigationContainerRef } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { AppState } from 'react-native';
import SCREENS from '~constants/screens';
import AppScreens from './AppScreens/AppScreens';
import AuthScreens from './AuthScreens';
import auth from '@react-native-firebase/auth';
import OnboardingScreens from './OnboardingScreens';
import { useGetUserAccount } from '~hooks/user/useGetUser';
import LoadingScreen from '~containers/Loading';
import { useGetBusinessAccount } from '~hooks/business/useGetBusinessAccount';
import { useMapsContext } from '~providers/maps/zustand';
import { ErrorBoundary } from '~components/ErrorBoundary';
import useGetCurrentPosition from '~components/LocationModal/hooks/useGetCurrentPosition';
import { useGetUserType } from '~hooks/event/useGetUserType';
import { useUserStore } from '~providers/userStore/zustand';
import VersionCheck from 'react-native-version-check';
import { Platform } from 'react-native';

import ChatProvider from '~providers/chats/context';
import axios from 'axios';
import UpdateAlert from '~components/UpdateAlert/UpdateAlert';
import { ThemedStatusBar } from '~components/ThemedStatusBar/ThemedStatusBar';
import { navigation, loading, auth as authLogger, timing, success } from '~Utils/debugLogger';
import { useOfflineUserData } from '~hooks/offline/useOfflineUserData';

// 5 mins
const SECONDS_TO_REFETCH_LOCATION = 300;
export const navigationRef = React.createRef<NavigationContainerRef<any>>();
const Navigation = () => {
  console.log('🧭 [DEBUG] Navigation component started rendering - basic log');
  try {
    navigation('Navigation component started rendering');
  } catch (error) {
    console.error('❌ [DEBUG] Error with navigation logger:', error);
  }
  const navigationStartTime = Date.now();

  const { setCurrentPositionState, currentPositionState } = useMapsContext();
  const { getCurrentPosition, getLocationName } = useGetCurrentPosition();
  const Stack = createNativeStackNavigator();
  const [isAuth, setIsAuth] = useState(!!auth().currentUser);
  const [appState, setAppState] = useState(AppState.currentState);

  navigation('Starting API calls for user data');
  const { data: userType, isLoading: userTypeIsLoading } = useGetUserType(auth().currentUser?.uid || '');
  const { data, isLoading: userAccountIsLoading } = useGetUserAccount(auth()?.currentUser?.uid || '');
  const { data: businessData, isLoading: businessIsLoading } = useGetBusinessAccount(auth()?.currentUser?.uid || '');

  // Get offline user data for fallback when offline
  const offlineUserData = useOfflineUserData();

  const [isNeedToUpdate, setIsNeedToUpdate] = useState(false); // Changed to false to prevent infinite loading
  const [showUpdateAlert, setShowUpdateAlert] = useState(false);
  const [updateStoreUrl, setUpdateStoreUrl] = useState('');

  const { setUser, setBusiness } = useUserStore();

  const isBusiness = userType === 'business';

  const showUpdateModal = useCallback((url: string) => {
    setUpdateStoreUrl(url);
    setShowUpdateAlert(true);
  }, []);

  const hideUpdateModal = useCallback(() => {
    setShowUpdateAlert(false);
    setUpdateStoreUrl('');
    setIsNeedToUpdate(false); // Reset the update flag so app can proceed
  }, []);

  const getIOSProvider = async (appStoreURL: string) => {
    try {
      const response = await axios.get(appStoreURL, {
        headers: {
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X)',
        },
      });
      const data = response.data;
      return data?.resultCount > 0 ? data.results : null;
    } catch (error) {
      return null;
    }
  };

  const checkVersionAndAskUpdate = useCallback(async () => {
    navigation('Starting version check');
    const versionCheckStart = Date.now();

    if (Platform.OS == 'android') {
      VersionCheck.needUpdate().then(async res => {
        timing('Android version check completed', versionCheckStart);
        navigation('Android version check result', res);

        setIsNeedToUpdate(res?.isNeeded);
        if (res?.isNeeded) {
          showUpdateModal(res.storeUrl);
        }
      });
    } else {
      const country = await VersionCheck.getCountry();
      const packageName = VersionCheck.getPackageName();
      const appStoreURL = `https://itunes.apple.com/${country}/lookup?bundleId=${packageName}`; // iTunes API URL of your package
      console.log(appStoreURL, 'appStoreURLappStoreURL');

      getIOSProvider(appStoreURL).then(res => {
        timing('iOS version check completed', versionCheckStart);
        navigation('iOS version check result', res[0]?.version);
        console.log(res[0].version, 'getIOSProvidergetIOSProvider');
        if (res[0]) {
          if (res[0].version) {
            VersionCheck.needUpdate({
              currentVersion: VersionCheck.getCurrentVersion(), // this returns the version setted on the last build
              latestVersion: res[0].version,
            }).then(resNeedUpdate => {
              navigation('Version update check result', resNeedUpdate);
              setIsNeedToUpdate(resNeedUpdate?.isNeeded);
              if (resNeedUpdate?.isNeeded) {
                showUpdateModal(res[0].trackViewUrl);
              }
            });
          }
        }
      });
    }
  }, [showUpdateModal]);

  useEffect(() => {
    checkVersionAndAskUpdate();
  }, [checkVersionAndAskUpdate]);

  useEffect(() => {
    if (businessData) {
      setBusiness(businessData);
    }

    if (data && !(data as unknown as { detail: string }).detail) {
      setUser(data);
    }
  }, [data, businessData, setBusiness, setUser]);

  useEffect(() => {
    authLogger('Setting up Firebase auth state listener');
    auth().onAuthStateChanged(user => {
      if (user) {
        success('User authenticated');
        setIsAuth(true);
      } else {
        authLogger('User not authenticated');
        setIsAuth(false);
      }
    });
  }, []);

  // Handle AppState changes to prevent React Host context errors
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (appState.match(/inactive|background/) && nextAppState === 'active') {
        console.log('App has come to the foreground!');
      } else if (nextAppState.match(/inactive|background/)) {
        console.log('App has gone to the background!');
      }
      setAppState(nextAppState);
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
    };
  }, [appState]);

  useEffect(() => {
    if (isBusiness) {
      return;
    }

    let interval = setInterval(() => getCurrentPosition(true), SECONDS_TO_REFETCH_LOCATION * 1000);

    return () => {
      clearInterval(interval);
    };
  }, [getCurrentPosition, isBusiness]);

  const fetchUserPosition = useCallback(async () => {
    if (!currentPositionState && data?.coords.lat && data?.coords.long) {
      if (data?.coords_real?.lat && data?.coords_real?.long) {
        const locationName = await getLocationName(data.coords_real.lat, data.coords_real.long);

        setCurrentPositionState({
          latitude: data.coords_real.lat,
          longitude: data.coords_real.long,
          address: locationName || '',
        });
      } else {
        const locationName = await getLocationName(data.coords.lat, data.coords.long);

        setCurrentPositionState({
          latitude: data.coords.lat,
          longitude: data.coords.long,
          address: locationName || '',
        });
      }

      getCurrentPosition(true, true);
    }
  }, [
    currentPositionState,
    data?.coords_real?.lat,
    data?.coords_real?.long,
    data?.coords?.lat,
    data?.coords?.long,
    getCurrentPosition,
    setCurrentPositionState,
    getLocationName,
  ]);

  const fetchBusinessPosition = useCallback(async () => {
    if (!currentPositionState && businessData?.coords.lat && businessData?.coords.long) {
      const locationName = await getLocationName(businessData.coords.lat, businessData.coords.long);

      setCurrentPositionState({
        latitude: businessData.coords.lat,
        longitude: businessData.coords.long,
        address: locationName || '',
      });
    }
  }, [
    businessData?.coords.lat,
    businessData?.coords.long,
    currentPositionState,
    getLocationName,
    setCurrentPositionState,
  ]);

  useEffect(() => {
    if (isBusiness) {
      fetchBusinessPosition();
    } else {
      fetchUserPosition();
    }
  }, [fetchUserPosition, fetchBusinessPosition, isBusiness]);

  const loadingIsActive = useMemo(() => {
    navigation('Checking loading states', {
      isAuth,
      userTypeIsLoading,
      userType,
      businessIsLoading,
      userAccountIsLoading,
      hasOfflineData: offlineUserData.hasOfflineData,
      isOffline: offlineUserData.isOffline,
      navigationTime: Date.now() - navigationStartTime,
    });

    if (!isAuth) {
      navigation('Not authenticated - no loading needed');
      return false;
    }

    // If we have offline data and we're offline, don't show loading
    if (offlineUserData.hasOfflineData && offlineUserData.isOffline) {
      success('Using offline data - no loading needed');
      return false;
    }

    // If we have offline data but we're online, still allow loading for fresh data
    // but don't block the UI if loading takes too long
    if (offlineUserData.hasOfflineData && !offlineUserData.isOffline) {
      const loadingTime = Date.now() - navigationStartTime;
      if (loadingTime > 3000) {
        // After 3 seconds, use offline data
        success('Loading taking too long - using offline data');
        return false;
      }
    }

    if (userTypeIsLoading) {
      loading('User type is loading');
      return true;
    }

    if (userType === 'business' && businessIsLoading) {
      loading('Business data is loading');
      return true;
    } else if (userType === 'business' && !businessIsLoading) {
      success('Business data loaded');
      return false;
    }

    if (userType === 'personal' && userAccountIsLoading) {
      loading('Personal user data is loading');
      return true;
    } else if (userType === 'personal' && !userAccountIsLoading) {
      success('Personal user data loaded');
      return false;
    }

    success('All loading completed');
    return false;
  }, [
    isAuth,
    businessIsLoading,
    userAccountIsLoading,
    userType,
    userTypeIsLoading,
    navigationStartTime,
    offlineUserData.hasOfflineData,
    offlineUserData.isOffline,
  ]);

  const screens = useMemo(() => {
    // Use offline data as fallback when online data is not available
    const currentUserData = data || offlineUserData.userData;
    const currentBusinessData = businessData || offlineUserData.businessData;
    const currentUserType = userType || offlineUserData.userType;

    console.log('🔄 [DEBUG] Determining which screen to show:', {
      loadingIsActive,
      isNeedToUpdate,
      isAuth,
      emailVerified: auth().currentUser?.emailVerified,
      userRegistrationFinished: currentUserData?.is_registration_finished,
      businessRegistrationFinished: currentBusinessData?.is_registration_finished,
      hasOnboardingAnswers: (currentUserData?.onboarding_answers?.length || 0) > 0,
      onboardingAnswers: currentUserData?.onboarding_answers,
      userData: currentUserData,
      businessData: currentBusinessData,
      userType: currentUserType,
      isOffline: offlineUserData.isOffline,
      hasOfflineData: offlineUserData.hasOfflineData,
      offlineDataAge: offlineUserData.lastSync,
    });

    if (loadingIsActive || isNeedToUpdate) {
      console.log('📱 [DEBUG] Showing LOADING screen');
      return <Stack.Screen name={SCREENS.LOADING} component={LoadingScreen} />;
    }
    if (!isAuth || !auth().currentUser?.emailVerified) {
      console.log('📱 [DEBUG] Showing AUTH screen');
      return <Stack.Screen name={SCREENS.AUTH} component={AuthScreens} />;
    }

    // Check registration status using current data (online or offline)
    const isRegistrationFinished =
      currentUserData?.is_registration_finished || currentBusinessData?.is_registration_finished;
    const hasOnboardingAnswers = currentUserData?.onboarding_answers && currentUserData.onboarding_answers.length > 0;

    if (!isRegistrationFinished) {
      console.log('📱 [DEBUG] Showing ONBOARDING screen (registration not finished)', {
        userRegistrationFinished: currentUserData?.is_registration_finished,
        businessRegistrationFinished: currentBusinessData?.is_registration_finished,
        usingOfflineData: !data && !!offlineUserData.userData,
      });
      return <Stack.Screen name={SCREENS.ONBOARDING} component={OnboardingScreens} />;
    }

    if (!hasOnboardingAnswers) {
      console.log('📱 [DEBUG] Showing ONBOARDING screen (no answers)', {
        onboardingAnswers: currentUserData?.onboarding_answers,
        answersLength: currentUserData?.onboarding_answers?.length || 0,
        usingOfflineData: !data && !!offlineUserData.userData,
      });
      return <Stack.Screen name={SCREENS.ONBOARDING} component={OnboardingScreens} />;
    }

    console.log('📱 [DEBUG] Showing APP_ROOT screen', {
      isRegistrationFinished,
      hasOnboardingAnswers,
      usingOfflineData: !data && !!offlineUserData.userData,
    });
    return <Stack.Screen name={SCREENS.APP_ROOT} component={AppScreens} />;
  }, [
    loadingIsActive,
    isAuth,
    data?.is_registration_finished,
    data?.onboarding_answers,
    businessData?.is_registration_finished,
    offlineUserData.userData?.is_registration_finished,
    offlineUserData.userData?.onboarding_answers,
    offlineUserData.businessData?.is_registration_finished,
    offlineUserData.hasOfflineData,
    offlineUserData.isOffline,
    userType,
    offlineUserData.userType,
    Stack,
    isNeedToUpdate,
  ]);

  return (
    <ErrorBoundary>
      <NavigationContainer ref={navigationRef}>
        <ChatProvider>
          <ThemedStatusBar />
          <Stack.Navigator screenOptions={{ headerShown: false, gestureEnabled: false, animation: 'default' }}>
            {screens}
          </Stack.Navigator>
          <UpdateAlert visible={showUpdateAlert} onDismiss={hideUpdateModal} storeUrl={updateStoreUrl} />
        </ChatProvider>
      </NavigationContainer>
    </ErrorBoundary>
  );
};

export default Navigation;
