import { useNavigation } from '@react-navigation/native';
import React, { useCallback, useEffect } from 'react';
import auth from '@react-native-firebase/auth';
// import {GoogleSignin} from '@react-native-google-signin/google-signin';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { NavigationProps } from '~types/navigation/navigation.type';
import { contactUs } from '~Utils/Settings';
import LoadingComponent from '~components/LoadingComponent';
import { SCREENS } from '~constants';
import { useMapsContext } from '~providers/maps/zustand';
import { useGetUserType } from '~hooks/event/useGetUserType';
import { useDeleteBusiness } from '~hooks/business/useDeleteBusiness';
import { useDeleteUser } from '~hooks/user/useDeleteUser';
import { useUpdateUser } from '~hooks/user/useUpdateUser';
import FirebaseAuth from '~services/FirebaseAuthService';

const DeleteLoading = () => {
  const { navigate } = useNavigation<NavigationProps>();
  const { t } = useTranslation();
  const { mutateAsync: updateUserMutation } = useUpdateUser();
  const { resetMapsState } = useMapsContext();
  const { data: userType } = useGetUserType(auth().currentUser?.uid);
  const { mutateAsync: deleteUser } = useDeleteUser();
  const { mutateAsync: deleteBusiness } = useDeleteBusiness();

  const isGoogleAuth = auth().currentUser?.providerData[0].providerId === 'google.com';
  const isAppleAuth = auth().currentUser?.providerData[0].providerId === 'apple.com';

  const deleteAccAsync = useCallback(async () => {
    if (!auth()?.currentUser?.uid) {
      return;
    }

    await updateUserMutation({
      coords_real: null,
    });
    resetMapsState();
    if (isGoogleAuth) {
      await FirebaseAuth.reauthenticateGoogle();
    } else if (isAppleAuth) {
      await FirebaseAuth.reauthenticateApple();
    }

    await auth()?.currentUser?.delete().catch(e => {
      Alert.alert(t('settings.deleteAccountErrorAlert.title'), t('settings.deleteAccountErrorAlert.description'), [
        { onPress: contactUs, text: t('settings.deleteAccountErrorAlert.contact'), isPreferred: true },
        { style: 'cancel', text: t('settings.deleteAccountErrorAlert.cancel') },
      ]);
    });
    deleteUser(auth().currentUser?.uid!);
    navigate(SCREENS.LOGIN);
  }, [isGoogleAuth, isAppleAuth, navigate, resetMapsState, t, updateUserMutation, deleteUser]);

  const deleteBusinessAsync = useCallback(async () => {
    if (!auth()?.currentUser?.uid) {
      return;
    }
    resetMapsState();
    if (isGoogleAuth) {
      await FirebaseAuth.reauthenticateGoogle();
    } else if (isAppleAuth) {
      await FirebaseAuth.reauthenticateApple();
    }

    await auth()?.currentUser?.delete().catch(e => {
      Alert.alert(t('settings.deleteAccountErrorAlert.title'), t('settings.deleteAccountErrorAlert.description'), [
        { onPress: contactUs, text: t('settings.deleteAccountErrorAlert.contact'), isPreferred: true },
        { style: 'cancel', text: t('settings.deleteAccountErrorAlert.cancel') },
      ]);
    });
    deleteBusiness(auth().currentUser!.uid);
    navigate(SCREENS.LOGIN);
  }, [isGoogleAuth, isAppleAuth, navigate, resetMapsState, t, deleteBusiness]);

  useEffect(() => {
    if (userType === 'business') {
      deleteBusinessAsync();
    } else {
      deleteAccAsync();
    }
  }, [userType, deleteBusinessAsync, deleteAccAsync]);

  return <LoadingComponent />;
};

export default DeleteLoading;
